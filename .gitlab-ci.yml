variables:
  GIT_STRATEGY: clone
  DOCKER_IMAGE_NAME: $DOCKER_REGISTRY/$PROJECT_GROUP/$CI_PROJECT_NAME
  CHART_SERVICE_TAG_PATH: $CHART_YAML_SELECTOR.$CI_PROJECT_NAME.image.tag

workflow:
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
      variables:
        CHART_GIT_URL: $CHART_DEV_URL
        CHART_VALUES_FILE: "values-dev.yaml"
        DOCKER_IMAGE_TAG: "dev-$CI_COMMIT_SHORT_SHA"

stages:
  - build
  - deploy

build:
  stage: build
  before_script:
    - docker login $DOCKER_REGISTRY -u $DOCKER_REGISTRY_USER -p $DOCKER_REGISTRY_PASSWD
  script:
    - docker build --build-arg APM_PHP_AGENT_VER=$APM_PHP_AGENT_VER -t $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG .
    - docker push $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"

deploy:
  stage: deploy
  variables:
    GIT_STRATEGY: none
  before_script:
    - rm -rf chart
  script:
    - git clone --depth=1 https://$RUNNER_GITLAB_USER:$RUNNER_GITLAB_TOKEN@$CHART_GIT_URL chart && cd chart
    - >
      docker run --rm
      --user="root"
      -v "${PWD}":/workspace
      -w /workspace
      $DOCKER_YQ_IMAGE
      eval ''$CHART_SERVICE_TAG_PATH' = "'$DOCKER_IMAGE_TAG'"' -i $CHART_VALUES_FILE
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "DevOps Team"
    - git add .
    - git commit -m "[BOT] Update $CI_PROJECT_NAME image tag to $DOCKER_IMAGE_TAG"
    - git push
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
