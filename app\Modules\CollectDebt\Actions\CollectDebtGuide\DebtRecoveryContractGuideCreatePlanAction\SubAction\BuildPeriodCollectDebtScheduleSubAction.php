<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtGuide\DebtRecoveryContractGuideCreatePlanAction\SubAction;

use Carbon\Carbon;
use App\Lib\Helper;
use Carbon\CarbonPeriod;
use Illuminate\Support\Arr;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Ultilities\ReminderUtil;

class BuildPeriodCollectDebtScheduleSubAction
{
  private $__totoalMoneyNeedPayment = 0;
  
  private $__removeNgayChuKyTrichNoSatNgayKetThucHd = '';
  /**
   * Tính toán lịch thu dựa vào chỉ dẫn
   *
   * @param CollectDebtGuide $collectDebtGuide [Bản ghi chỉ dẫn]
   * @param int $totalPeriod [Số kỳ thu nợ mà MC cần thanh toán]
   *
   * @return array 
   * $scheduleInfo = [
   *    [
   *      'payment_date' => 'Y-m-d H:i:s',
          'money_need_payment' => float,
          'money_need_payment_as_price' => string
   *    ]
   * ]
   */
  public function run(CollectDebtGuide $collectDebtGuide): array
  {
    $listPeriodNeedPayment = [];

    $T = $collectDebtGuide->time_start_as_date->copy(); // ngày bắt đầu HĐ

    $totalDateBetweenTdayAndTimeEnd = CarbonPeriod::create($T, $collectDebtGuide->time_end_as_date)->toArray();

    $period = $collectDebtGuide->contract_intervals;
    
    while( true ) {
      // Xử lý `key` để tránh việc có 2 bản ghi chu kỳ trích nợ bị trùng
      if (isset($totalDateBetweenTdayAndTimeEnd[$period])) {
        $key = $totalDateBetweenTdayAndTimeEnd[$period]->toDateString();
        $listPeriodNeedPayment[$key] = $totalDateBetweenTdayAndTimeEnd[$period];
        $period += $collectDebtGuide->contract_intervals;
      }else {
        $key = Arr::last($totalDateBetweenTdayAndTimeEnd)->toDateString();
        $listPeriodNeedPayment[$key] = Arr::last($totalDateBetweenTdayAndTimeEnd);
        break;
      }
    }

    $ngayKetThucHopDong = $collectDebtGuide->time_end_as_date->copy()->startOfDay();

    // Đoạn này cần xử lý, nếu chu kỳ trích nợ hiện tại + 1 ngày => bằng chu kỳ cuối cùng (ngày kỳ TT hĐ thì bỏ)
    foreach ($listPeriodNeedPayment as $ngayChuKyTrichNo => $chuKyTrichNo) {
      $ngayChuKyTrichNoCarbon = Carbon::parse($ngayChuKyTrichNo);
      if ($ngayKetThucHopDong->diffInDays($ngayChuKyTrichNoCarbon) == 1) {
        $this->__removeNgayChuKyTrichNoSatNgayKetThucHd =  $ngayChuKyTrichNo ;
      }
    }

    if ($this->__removeNgayChuKyTrichNoSatNgayKetThucHd) {
      unset($listPeriodNeedPayment[$this->__removeNgayChuKyTrichNoSatNgayKetThucHd]);
    }
    
    $listPeriodNeedPayment = array_values($listPeriodNeedPayment);

    $totalPeriod = count($listPeriodNeedPayment);
    $averageAmountToBePaidPerPeriod = round($collectDebtGuide->amount / $totalPeriod);

    foreach ($listPeriodNeedPayment as $currentPeriod => $periodPaymentDate) {
      if ($currentPeriod != $totalPeriod-1) {
        // Không phải kỳ cuối. -1 là do mảng đếm từ vị trí 0
        $this->__totoalMoneyNeedPayment += $averageAmountToBePaidPerPeriod;

        $scheduleInfo[] = [
          'collect_debt_schedule'      => $periodPaymentDate,
          'amount_to_be_paid'          => $averageAmountToBePaidPerPeriod,
          'amount_to_be_paid_as_price' => Helper::priceFormat($averageAmountToBePaidPerPeriod),
          
          'debit_begin'                => $collectDebtGuide->amount - $currentPeriod *  $averageAmountToBePaidPerPeriod,
          'debit_end'                  => $collectDebtGuide->amount - $this->__totoalMoneyNeedPayment,
          'is_settlement'              => 0,
          'time_start'                 => $periodPaymentDate->copy()->startOfDay()->timestamp,
          'time_end'                   => $periodPaymentDate->copy()->endOfDay()->timestamp,
          'rundate'                    => $collectDebtGuide->isHopDongNextlend() ? $periodPaymentDate->copy()->addDay()->format('Ymd')
																																								 : $periodPaymentDate->copy()->format('Ymd'),
          'cycle_number'               => $currentPeriod,
          'other_data'                 => json_encode([
            [
              'type' => 'OTHER',
              'data' => [
                'request_created_channel' => '', // Yêu cầu được tạo qua kênh
              ],
              'time_modified' => time(),
              'note' => 'Tạo lịch từ chỉ dẫn'
            ],
          ])
        ];
      } else {
        // Kỳ cuối
        $periodPaymentDate = $collectDebtGuide->time_end_as_date;
        $scheduleInfo[] = [
          'collect_debt_schedule'      => $periodPaymentDate,
          'amount_to_be_paid'          => $collectDebtGuide->amount - $this->__totoalMoneyNeedPayment,
          'amount_to_be_paid_as_price' => Helper::priceFormat($collectDebtGuide->amount - $this->__totoalMoneyNeedPayment),
          
          'debit_begin'                => $collectDebtGuide->amount - $currentPeriod *  $averageAmountToBePaidPerPeriod,
          'debit_end'                  => 0,
          'is_settlement'              => 1,
          'time_start'                 => $periodPaymentDate->copy()->startOfDay()->timestamp,
          'time_end'                   => $periodPaymentDate->copy()->endOfDay()->timestamp,
          'rundate'                    => $collectDebtGuide->isHopDongNextlend() ? $periodPaymentDate->copy()->addDay()->format('Ymd') 
																																								 : $periodPaymentDate->copy()->format('Ymd'),
          'cycle_number'               => $currentPeriod,
          'other_data'                 => json_encode([
            [
              'type' => 'OTHER',
              'data' => [
                'request_created_channel' => '', // Yêu cầu được tạo qua kênh
              ],
              'time_modified' => time(),
              'note' => 'Tạo lịch từ chỉ dẫn'
            ],
          ])
        ];
      }
    }

    return $scheduleInfo;
  } // End method
} // End class
