<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction;


use Exception;
use React\EventLoop\Factory;
use function React\Async\coroutine;
use function React\Promise\resolve;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFindRawQueryAction\SubAction\GetRequestByRawQueryAsCollectionSubAction;

class DebtRecoveryRequestCheckPaymentAction
{
	private array $__processedIds = [];

	private array $__errorIds = [];

	public function run()
	{
		$loop = Factory::create();
		$collectDebtRequests = $this->getRequests();
		$done = 0;
		$total = $collectDebtRequests->count();

		foreach ($collectDebtRequests as $collectDebtRequest) {
			coroutine(function () use ($collectDebtRequest, &$done, $total, $loop) {
				try {
					yield resolve(null); // Cho nhả loop 1 tick
					self::handleCheck($collectDebtRequest);
					$this->__processedIds[] = $collectDebtRequest->partner_request_id;
				} catch (\Throwable $e) {
					$this->__errorIds[] = "$collectDebtRequest->partner_request_id ----> $e->getMessage()";
				} finally {
					$done++;
					if ($done >= $total) {
						$loop->stop();
					}
				}
			});
		}

		$loop->run();

		return [
			'processed_ids' => $this->__processedIds,
			'error_ids' => $this->__errorIds
		];
	}

	protected function getRequests()
	{
		$whereRaw = sprintf(
      "payment_method_code = 'MPOS' AND (status = %s OR status = %s) 
                                    AND status_payment = %s 
                                    AND time_receivered IS NULL 
                                    AND (time_checked IS NULL OR time_checked + %d < %d)",

      CollectDebtEnum::REQUEST_STT_MOI_TAO, CollectDebtEnum::REQUEST_STT_DA_DUYET,

      CollectDebtEnum::REQUEST_STT_PM_DA_GUI,
      
      env('THOI_GIAN_CHECK_LAI_YEU_CAU_TRICH', 30) * 60, time()
    );

		$collectDebtRequests = app(GetRequestByRawQueryAsCollectionSubAction::class)->run(
			$whereRaw,
			30,
			['*']
		);

		throw_if($collectDebtRequests->isEmpty(), new Exception('Không có yêu cầu cần check trạng thái trích nợ'));

		$collectDebtRequests->load('collectDebtPartner');
		return $collectDebtRequests;
	}

	public static function handleCheck(CollectDebtRequest $collectDebtRequest)
	{
		$request = request();


		if (!$collectDebtRequest->collectDebtPartner) {
			$checked = app(CheckRequestViaMposSubAction::class)->run($collectDebtRequest, $request);
			$collectDebtRequest->mpos_debt_result = $checked;
		}

			
		if ($collectDebtRequest->collectDebtPartner) {
			$collectDebtRequest->forceFill(['time_checked' => time()])->update();
		}

		return $collectDebtRequest;
	}
}
